import React, { useEffect, useRef, useState } from "react";
import { MdAttachFile, MdS<PERSON>, MdLogout, MdVideocam } from "react-icons/md";
import useChatContext from "../context/ChatContext";
import { useNavigate } from "react-router";
import SockJS from "sockjs-client";
import { Stomp } from "@stomp/stompjs";
import toast from "react-hot-toast";
import { baseURL } from "../config/AxiosHelper";
import { getMessagess } from "../services/RoomService";
import { timeAgo } from "../config/helper";
import VideoCall from "./VideoCall";

const ChatPage = () => {
  const {
    roomId,
    currentUser,
    connected,
    setConnected,
    setRoomId,
    setCurrentUser,
  } = useChatContext();
  
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [stompClient, setStompClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInVideoCall, setIsInVideoCall] = useState(false);
  const navigate = useNavigate();
  const chatBoxRef = useRef();
  const inputRef = useRef();

  // Load historical messages when joining a room
  useEffect(() => {
    if (!connected) {
      navigate("/");
      return;
    }

    const loadMessages = async () => {
      setIsLoading(true);
      try {
        // Fetch all messages with a larger page size to ensure we get history
        const data = await getMessagess(roomId, 100, 0);
        if (Array.isArray(data)) {
          setMessages(data);
        } else if (data && Array.isArray(data.content)) {
          setMessages(data.content);
        } else {
          setMessages([]);
        }
      } catch (error) {
        console.log(error);
        toast.error("Failed to load messages");
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [roomId, connected, navigate]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (chatBoxRef.current) {
      chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight;
    }
  }, [messages]);

  // Connect to WebSocket
  useEffect(() => {
    const connectWebSocket = () => {
      const sock = new SockJS(`${baseURL}/chat`);
      const client = Stomp.over(sock);

      client.connect({}, () => {
        setStompClient(client);
        toast.success("Connected to chat");

        client.subscribe(`/topic/room/${roomId}`, (message) => {
          const newMessage = JSON.parse(message.body);
          setMessages((prev) => [...prev, newMessage]);
        });
      });
    };

    if (connected) {
      connectWebSocket();
    }

    return () => {
      if (stompClient) {
        stompClient.disconnect();
      }
    };
  }, [roomId, connected]);

  const sendMessage = async () => {
    if (stompClient && connected && input.trim()) {
      const message = {
        sender: currentUser,
        content: input,
        roomId: roomId,
      };

      stompClient.send(
        `/app/sendMessage/${roomId}`,
        {},
        JSON.stringify(message)
      );
      setInput("");
      inputRef.current?.focus();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  function handleLogout() {
    if (stompClient) {
      stompClient.disconnect();
    }
    setConnected(false);
    setRoomId("");
    setCurrentUser("");
    navigate("/");
  }

  const startVideoCall = () => {
    if (!connected) {
      toast.error("You must be connected to start a video call");
      return;
    }
    
    // Check if browser supports WebRTC
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      toast.error("Your browser doesn't support video calls");
      return;
    }
    
    setIsInVideoCall(true);
    
    // Notify other users about the video call
    if (stompClient) {
      const message = {
        sender: "System",
        content: `${currentUser} started a video call. Join now!`,
        roomId: roomId,
      };

      stompClient.send(
        `/app/sendMessage/${roomId}`,
        {},
        JSON.stringify(message)
      );
    }
  };

  const endVideoCall = () => {
    setIsInVideoCall(false);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
      {/* Video Call Component */}
      {isInVideoCall && (
        <VideoCall 
          roomId={roomId}
          username={currentUser}
          stompClient={stompClient}
          onEndCall={endVideoCall}
        />
      )}
      
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-md py-4 px-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-bold text-gray-800 dark:text-white">Room: {roomId}</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">Logged in as {currentUser}</p>
          </div>
          <div className="flex gap-2">
            <button 
              onClick={startVideoCall}
              className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              <MdVideocam /> Video Call
            </button>
            <button 
              onClick={handleLogout}
              className="flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <MdLogout /> Logout
            </button>
          </div>
        </div>
      </header>

      {/* Chat Messages */}
      <main
        ref={chatBoxRef}
        className="flex-1 overflow-y-auto p-6 space-y-4"
      >
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <p className="text-xl font-medium">No messages yet</p>
            <p className="text-sm">Be the first to send a message!</p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div
              key={index}
              className={`flex ${
                message.sender === currentUser ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`message-bubble ${
                  message.sender === currentUser 
                    ? "bg-indigo-600 text-white" 
                    : "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white"
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-indigo-300 dark:bg-indigo-700 flex items-center justify-center text-white font-bold">
                      {message.sender.charAt(0).toUpperCase()}
                    </div>
                  </div>
                  <div>
                    <p className="text-xs font-bold mb-1">{message.sender}</p>
                    <p className="break-words">{message.content}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {timeAgo(message.timeStamp)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </main>

      {/* Message Input */}
      <div className="bg-white dark:bg-gray-800 p-4 border-t dark:border-gray-700">
        <div className="flex gap-2">
          <button className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
            <MdAttachFile size={20} />
          </button>
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Type a message..."
            className="flex-1 py-2 px-4 bg-gray-100 dark:bg-gray-700 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:text-white"
          />
          <button
            onClick={sendMessage}
            disabled={!input.trim()}
            className={`p-2 rounded-full ${
              input.trim() 
                ? "bg-indigo-600 text-white" 
                : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
            }`}
          >
            <MdSend size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;




package com.substring.chat.controllers;

import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;

import java.util.Map;

@Controller
@CrossOrigin("http://localhost:5173")
public class VideoCallController {

    // Handle WebRTC signaling for video calls
    @MessageMapping("/call/{roomId}")
    @SendTo("/topic/call/{roomId}")
    public Map<String, Object> processCallSignal(
            @DestinationVariable String roomId,
            Map<String, Object> signal
    ) {
        // Simply relay the WebRTC signaling data to all clients in the room
        return signal;
    }
}
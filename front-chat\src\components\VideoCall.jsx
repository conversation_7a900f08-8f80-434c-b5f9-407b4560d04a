import React, { useEffect, useRef, useState } from 'react';
import { MdCallEnd, MdVideocam, MdVideocamOff, MdMic, MdMicOff } from 'react-icons/md';

const VideoCall = ({ roomId, username, stompClient, onEndCall }) => {
  const [localStream, setLocalStream] = useState(null);
  const [remoteStreams, setRemoteStreams] = useState({});
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  
  const localVideoRef = useRef(null);
  const peerConnections = useRef({});
  
  // Initialize local media stream
  useEffect(() => {
    const startLocalStream = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: true, 
          audio: true 
        });
        
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
        
        setLocalStream(stream);
        
        // Announce to the room that a new user has joined the call
        if (stompClient) {
          stompClient.send(
            `/app/call/${roomId}`,
            {},
            JSON.stringify({
              type: 'join',
              from: username,
              roomId: roomId
            })
          );
        }
      } catch (err) {
        console.error('Error accessing media devices:', err);
      }
    };
    
    startLocalStream();
    
    return () => {
      // Clean up local stream when component unmounts
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [roomId, username]);
  
  // Set up WebRTC signaling via STOMP
  useEffect(() => {
    if (!stompClient || !localStream) return;
    
    const handleSignalingData = async (message) => {
      const signal = JSON.parse(message.body);
      const { type, from, roomId: signalRoomId } = signal;
      
      // Ignore messages from self
      if (from === username) return;
      
      // Handle different signal types
      switch (type) {
        case 'join':
          // A new user joined, send them an offer
          createPeerConnection(from);
          break;
          
        case 'offer':
          // Received an offer, create answer
          if (!peerConnections.current[from]) {
            createPeerConnection(from);
          }
          
          await peerConnections.current[from].setRemoteDescription(
            new RTCSessionDescription(signal.offer)
          );
          
          const answer = await peerConnections.current[from].createAnswer();
          await peerConnections.current[from].setLocalDescription(answer);
          
          stompClient.send(
            `/app/call/${roomId}`,
            {},
            JSON.stringify({
              type: 'answer',
              from: username,
              to: from,
              answer: answer,
              roomId: roomId
            })
          );
          break;
          
        case 'answer':
          // Received an answer to our offer
          if (signal.to === username && peerConnections.current[from]) {
            await peerConnections.current[from].setRemoteDescription(
              new RTCSessionDescription(signal.answer)
            );
          }
          break;
          
        case 'ice-candidate':
          // Add ICE candidate received from peer
          if (signal.to === username && peerConnections.current[from]) {
            await peerConnections.current[from].addIceCandidate(
              new RTCIceCandidate(signal.candidate)
            );
          }
          break;
          
        case 'leave':
          // Peer left the call
          if (peerConnections.current[from]) {
            peerConnections.current[from].close();
            delete peerConnections.current[from];
            
            setRemoteStreams(prev => {
              const newStreams = { ...prev };
              delete newStreams[from];
              return newStreams;
            });
          }
          break;
      }
    };
    
    // Subscribe to signaling channel
    const subscription = stompClient.subscribe(
      `/topic/call/${roomId}`,
      handleSignalingData
    );
    
    return () => {
      // Unsubscribe and send leave message when component unmounts
      if (subscription) {
        subscription.unsubscribe();
      }
      
      if (stompClient) {
        stompClient.send(
          `/app/call/${roomId}`,
          {},
          JSON.stringify({
            type: 'leave',
            from: username,
            roomId: roomId
          })
        );
      }
      
      // Close all peer connections
      Object.values(peerConnections.current).forEach(pc => pc.close());
      peerConnections.current = {};
    };
  }, [stompClient, localStream, roomId, username]);
  
  // Create a new WebRTC peer connection
  const createPeerConnection = async (peerId) => {
    try {
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' }
        ]
      });
      
      // Add local tracks to the connection
      if (localStream) {
        localStream.getTracks().forEach(track => {
          pc.addTrack(track, localStream);
        });
      }
      
      // Handle ICE candidates
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          stompClient.send(
            `/app/call/${roomId}`,
            {},
            JSON.stringify({
              type: 'ice-candidate',
              from: username,
              to: peerId,
              candidate: event.candidate,
              roomId: roomId
            })
          );
        }
      };
      
      // Handle incoming tracks
      pc.ontrack = (event) => {
        setRemoteStreams(prev => ({
          ...prev,
          [peerId]: event.streams[0]
        }));
      };
      
      peerConnections.current[peerId] = pc;
      
      // If this is the peer that initiated the connection, create and send an offer
      if (localStream) {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        
        stompClient.send(
          `/app/call/${roomId}`,
          {},
          JSON.stringify({
            type: 'offer',
            from: username,
            to: peerId,
            offer: offer,
            roomId: roomId
          })
        );
      }
    } catch (err) {
      console.error('Error creating peer connection:', err);
    }
  };
  
  // Toggle audio mute
  const toggleAudio = () => {
    if (localStream) {
      localStream.getAudioTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
      setIsAudioMuted(!isAudioMuted);
    }
  };
  
  // Toggle video on/off
  const toggleVideo = () => {
    if (localStream) {
      localStream.getVideoTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
      setIsVideoOff(!isVideoOff);
    }
  };
  
  // End the call
  const endCall = () => {
    if (stompClient) {
      stompClient.send(
        `/app/call/${roomId}`,
        {},
        JSON.stringify({
          type: 'leave',
          from: username,
          roomId: roomId
        })
      );
    }
    
    // Stop all tracks
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }
    
    // Close all peer connections
    Object.values(peerConnections.current).forEach(pc => pc.close());
    peerConnections.current = {};
    
    onEndCall();
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex flex-col">
      <div className="p-4 flex justify-between items-center bg-gray-900">
        <h2 className="text-white text-lg font-semibold">Video Call - Room: {roomId}</h2>
        <button 
          onClick={endCall}
          className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-full"
        >
          <MdCallEnd size={24} />
        </button>
      </div>
      
      <div className="flex-1 p-4 flex flex-wrap gap-4 justify-center content-start overflow-auto">
        {/* Local video */}
        <div className="relative w-64 h-48 bg-gray-800 rounded-lg overflow-hidden">
          <video
            ref={localVideoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 px-2 py-1 rounded text-white text-xs">
            {username} (You)
          </div>
        </div>
        
        {/* Remote videos */}
        {Object.entries(remoteStreams).map(([peerId, stream]) => (
          <div key={peerId} className="relative w-64 h-48 bg-gray-800 rounded-lg overflow-hidden">
            <video
              autoPlay
              playsInline
              className="w-full h-full object-cover"
              ref={el => {
                if (el) el.srcObject = stream;
              }}
            />
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 px-2 py-1 rounded text-white text-xs">
              {peerId}
            </div>
          </div>
        ))}
      </div>
      
      <div className="p-4 bg-gray-900 flex justify-center gap-4">
        <button 
          onClick={toggleAudio}
          className={`p-3 rounded-full ${isAudioMuted ? 'bg-red-600' : 'bg-gray-700'}`}
        >
          {isAudioMuted ? <MdMicOff size={24} color="white" /> : <MdMic size={24} color="white" />}
        </button>
        <button 
          onClick={toggleVideo}
          className={`p-3 rounded-full ${isVideoOff ? 'bg-red-600' : 'bg-gray-700'}`}
        >
          {isVideoOff ? <MdVideocamOff size={24} color="white" /> : <MdVideocam size={24} color="white" />}
        </button>
      </div>
    </div>
  );
};

export default VideoCall;